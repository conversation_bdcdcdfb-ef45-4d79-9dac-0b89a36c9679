/*
 * Copyright 2014 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

@interface ZXByteArray : NSObject

@property (nonatomic, assign, readonly) int8_t *array;
@property (nonatomic, assign, readonly) unsigned int length;

- (id)initWithLength:(unsigned int)length;
- (id)initWithArray:(int8_t *)array length:(unsigned int)length;
- (id)initWithBytes:(int)byte1, ...;
- (id)initWithLength:(unsigned int)length bytes:(int)byte1, ...;

@end
