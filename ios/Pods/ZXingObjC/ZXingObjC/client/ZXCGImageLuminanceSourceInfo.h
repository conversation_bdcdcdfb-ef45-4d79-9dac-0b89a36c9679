/*
 * Copyright 2018 ZXing contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

typedef enum {
  ZXCGImageLuminanceSourceNormal = 0,
  ZXCGImageLuminanceSourceLuma,
  ZXCGImageLuminanceSourceShades,
  ZXCGImageLuminanceSourceDigital,
  ZXCGImageLuminanceSourceDecomposingMax,
  ZXCGImageLuminanceSourceDecomposingMin,
} ZXCGImageLuminanceSourceType;


@interface ZXCGImageLuminanceSourceInfo : NSObject

@property (nonatomic, assign, readonly) uint32_t numberOfShades;

@property (nonatomic, assign, readonly) ZXCGImageLuminanceSourceType type;

- (instancetype)init NS_UNAVAILABLE;

- (instancetype)initWithLuma;

- (instancetype)initWithShades: (uint32_t)numberOfShades;

- (instancetype)initWithNormal;

- (instancetype)initWithDigital;

- (instancetype)initWithDecomposingMax;

- (instancetype)initWithDecomposingMin;

@end
